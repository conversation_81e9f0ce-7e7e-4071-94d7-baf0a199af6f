import Keycloak from 'keycloak-js'
import { keycloakConfig, keycloakInitOptions } from '@/config/keycloak'

class KeycloakService {
  constructor() {
    this.keycloak = null
    this.initialized = false
  }

  // Initialize Keycloak
  async init() {
    if (this.initialized) {
      return this.keycloak
    }

    this.keycloak = new Keycloak(keycloakConfig)
    
    try {
      const authenticated = await this.keycloak.init(keycloakInitOptions)
      this.initialized = true
      
      // Set up token refresh
      this.setupTokenRefresh()
      
      console.log('Keycloak initialized successfully', { authenticated })
      return this.keycloak
    } catch (error) {
      console.error('Failed to initialize Keycloak', error)
      throw error
    }
  }

  // Setup automatic token refresh
  setupTokenRefresh() {
    if (!this.keycloak) return

    // Refresh token when it's about to expire (30 seconds before)
    setInterval(() => {
      if (this.keycloak.authenticated) {
        this.keycloak.updateToken(30).then((refreshed) => {
          if (refreshed) {
            console.log('Token refreshed')
          }
        }).catch(() => {
          console.log('Failed to refresh token')
          this.logout()
        })
      }
    }, 10000) // Check every 10 seconds
  }

  // Login
  async login() {
    if (!this.keycloak) {
      await this.init()
    }
    return this.keycloak.login()
  }

  // Logout
  async logout() {
    if (!this.keycloak) return
    return this.keycloak.logout()
  }

  // Get user info
  getUserInfo() {
    if (!this.keycloak || !this.keycloak.authenticated) {
      return null
    }

    const tokenParsed = this.keycloak.tokenParsed
    return {
      id: tokenParsed.sub,
      username: tokenParsed.preferred_username,
      name: tokenParsed.name || tokenParsed.preferred_username,
      email: tokenParsed.email,
      roles: tokenParsed.realm_access?.roles || [],
      permissions: this.extractPermissions(tokenParsed)
    }
  }

  // Extract permissions from token
  extractPermissions(tokenParsed) {
    const permissions = []
    
    // Extract from realm roles
    if (tokenParsed.realm_access?.roles) {
      permissions.push(...tokenParsed.realm_access.roles)
    }
    
    // Extract from resource access
    if (tokenParsed.resource_access) {
      Object.keys(tokenParsed.resource_access).forEach(client => {
        const clientRoles = tokenParsed.resource_access[client].roles || []
        permissions.push(...clientRoles)
      })
    }
    
    return permissions
  }

  // Get access token
  getToken() {
    return this.keycloak?.token
  }

  // Check if user is authenticated
  isAuthenticated() {
    return this.keycloak?.authenticated || false
  }

  // Check if user has role
  hasRole(role) {
    return this.keycloak?.hasRealmRole(role) || false
  }

  // Get Keycloak instance
  getKeycloak() {
    return this.keycloak
  }
}

// Create singleton instance
const keycloakService = new KeycloakService()

export default keycloakService
