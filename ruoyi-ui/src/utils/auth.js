import Cookies from 'js-cookie'
import keycloakService from '@/services/keycloak'

const TokenKey = 'Admin-Token'
const ExpiresInKey = 'Admin-Expires-In'
const AuthModeKey = 'Auth-Mode' // 'traditional' or 'keycloak'

export function getToken() {
  const authMode = getAuthMode()
  if (authMode === 'keycloak') {
    return keycloakService.getToken()
  }
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  Cookies.remove(TokenKey)
  removeAuthMode()
  return true
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}

export function getAuthMode() {
  return Cookies.get(AuthModeKey) || 'traditional'
}

export function setAuthMode(mode) {
  return Cookies.set(AuthModeKey, mode)
}

export function removeAuthMode() {
  return Cookies.remove(AuthModeKey)
}

export function isKeycloakAuth() {
  return getAuthMode() === 'keycloak'
}
