import { login, logout, getInfo, refreshToken } from '@/api/login'
import { getToken, setToken, setExpiresIn, removeToken } from '@/utils/auth'
import { isEmpty } from "@/utils/validate"
import defAva from '@/assets/images/profile.jpg'
import keycloakService from '@/services/keycloak'

const user = {
  state: {
    token: getToken(),
    id: '',
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time
    },
    SET_ID: (state, id) => {
      state.id = id
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 传统登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          let data = res.data
          setToken(data.access_token)
          commit('SET_TOKEN', data.access_token)
          setExpiresIn(data.expires_in)
          commit('SET_EXPIRES_IN', data.expires_in)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // Keycloak登录
    KeycloakLogin({ commit, dispatch }) {
      return new Promise(async (resolve, reject) => {
        try {
          await keycloakService.login()

          // 获取用户信息
          const userInfo = keycloakService.getUserInfo()
          if (userInfo) {
            const token = keycloakService.getToken()
            setToken(token)
            commit('SET_TOKEN', token)
            commit('SET_ID', userInfo.id)
            commit('SET_NAME', userInfo.name)
            commit('SET_ROLES', userInfo.roles)
            commit('SET_PERMISSIONS', userInfo.permissions)
            commit('SET_AVATAR', '')
          }

          resolve()
        } catch (error) {
          reject(error)
        }
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const avatar = (isEmpty(user.avatar)) ? defAva : user.avatar
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_ID', user.userId)
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', '')
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 刷新token
    RefreshToken({commit, state}) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token).then(res => {
          setExpiresIn(res.data)
          commit('SET_EXPIRES_IN', res.data)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    
    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    },

    // Keycloak登出
    KeycloakLogout({ commit }) {
      return new Promise(async (resolve, reject) => {
        try {
          await keycloakService.logout()
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_ID', '')
          commit('SET_NAME', '')
          commit('SET_AVATAR', '')
          removeToken()
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    },

    // 初始化Keycloak用户信息
    InitKeycloakUser({ commit }) {
      return new Promise((resolve) => {
        if (keycloakService.isAuthenticated()) {
          const userInfo = keycloakService.getUserInfo()
          const token = keycloakService.getToken()

          if (userInfo && token) {
            setToken(token)
            commit('SET_TOKEN', token)
            commit('SET_ID', userInfo.id)
            commit('SET_NAME', userInfo.name)
            commit('SET_ROLES', userInfo.roles)
            commit('SET_PERMISSIONS', userInfo.permissions)
            commit('SET_AVATAR', '')
          }
        }
        resolve()
      })
    }
  }
}

export default user
