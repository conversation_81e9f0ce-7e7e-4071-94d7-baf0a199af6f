// Keycloak configuration
const keycloakConfig = {
  url: process.env.VUE_APP_KEYCLOAK_URL || 'http://localhost:8089',
  realm: process.env.VUE_APP_KEYCLOAK_REALM || 'ruoyi',
  clientId: process.env.VUE_APP_KEYCLOAK_CLIENT_ID || 'ruoyi-frontend'
}

// Keycloak initialization options
const keycloakInitOptions = {
  onLoad: 'check-sso',
  silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
  checkLoginIframe: false,
  pkceMethod: 'S256'
}

export { keycloakConfig, keycloakInitOptions }
